# 农村两轮车监控系统 - 完整使用手册

## 🎯 系统概述

本系统提供完整的视频监控解决方案，包括：
- **智能推流** - 按需推流，前端关闭后自动停止
- **音频采集** - USB音频设备支持，环境音频同步推送
- **WebRTC通话** - 实时语音通话功能
- **设备管理** - 多摄像头统一管理
- **冲突解决** - PulseAudio虚拟化，音频设备无冲突共享

## 👤 脚本运行用户指南

**重要提示：** 不同脚本需要使用不同的用户身份运行，请严格按照以下要求执行：

### 用户权限分配表

| 脚本名称 | 运行用户 | 原因 | 命令示例 |
|---------|---------|------|---------|
| `smart-stream-deploy.sh` | **root** | 需要创建系统服务、配置日志轮转 | `sudo ./smart-stream-deploy.sh` |
| `system-maintenance.sh` | **root** | 需要管理系统服务、重启服务 | `sudo ./system-maintenance.sh` |
| `webrtc.sh` | **orangepi** | 创建用户级自启动、运行浏览器 | `su - orangepi && ./webrtc.sh` |
| `setup_pavucontrol_auto_start.sh` | **orangepi** | 音频面板需要在用户桌面环境运行 | `su - orangepi && ./setup_pavucontrol_auto_start.sh` |

### 正确的部署顺序

```bash
# 第一步：部署推流系统（root用户）
sudo ./板子/劝导站智能推流/smart-stream-deploy.sh

# 第二步：切换到orangepi用户
su - orangepi
cd /home/<USER>/srs

# 第三步：部署WebRTC服务（orangepi用户）
./板子/webrtc.sh

# 第四步：部署音频控制面板（orangepi用户）
./板子/setup_pavucontrol_auto_start.sh

# 第五步：切换回root用户进行系统管理
exit
sudo ./板子/劝导站智能推流/system-maintenance.sh
```

### 常见错误及解决方案

**错误1：chromium无法启动**
```
[ERROR] Running as root without --no-sandbox is not supported
```
**解决：** 切换到orangepi用户运行WebRTC脚本

**错误2：权限不足**
```
[ERROR] Permission denied
```
**解决：** 使用sudo运行需要root权限的脚本

**错误3：文件不存在**
```
[ERROR] No such file or directory
```
**解决：** 确保在正确的目录下运行脚本

## 🚀 快速部署

### 准备工作：设置脚本执行权限
```bash
# 进入项目目录
cd /home/<USER>/srs

# 给所有脚本添加执行权限
chmod +x setup_pavucontrol_auto_start.sh
chmod +x smart-stream-deploy.sh
chmod +x system-maintenance.sh
chmod +x webrtc.sh
如果出现空格，可以运行
# 1. 安装 dos2unix
sudo apt update && sudo apt install dos2unix -y

# 2. 转换文件格式
dos2unix ./setup_pavucontrol_auto_start.sh

# 3. 执行脚本
sudo ./setup_pavucontrol_auto_start.sh
```

### 第一步：部署智能推流系统（root用户）
```bash
sudo ./板子/劝导站智能推流/smart-stream-deploy.sh
```

### 第二步：部署WebRTC语音通话（orangepi用户）
```bash
# 切换到orangepi用户
su - orangepi
cd /home/<USER>/srs
./板子/webrtc.sh
```

### 第三步：配置音频控制面板（orangepi用户，可选）
```bash
# 在orangepi用户环境下执行
./板子/setup_pavucontrol_auto_start.sh
```

部署过程中会询问：
- 摄像头数量和配置信息
- 是否启用音频采集
- 音频配置方式（推荐选择PulseAudio虚拟化）

### 第四步：验证部署结果（root用户）
```bash
# 切换回root用户
exit
# 使用维护工具检查系统状态
sudo ./板子/劝导站智能推流/system-maintenance.sh
# 选择 "9. 查看系统状态"
```

**注意：** 如果提示权限不足，请确保已执行准备工作中的权限设置步骤。

## 🎤 音频功能详解

### 硬件准备
**推荐设备：**
- USB声卡（带麦克风输入）
- USB麦克风（全向麦克风）
- USB音频适配器（3.5mm转USB）

**设备要求：**
- 支持Linux ALSA驱动
- USB 2.0或更高版本接口

### 音频配置选项
**选项1：直接ALSA设备**
- 直接访问物理音频设备
- 可能与WebRTC语音通话冲突

**选项2：PulseAudio虚拟化（推荐）**
- 创建虚拟音频源：`countryside_audio_source`
- 支持多应用同时访问
- WebRTC和推流无冲突

### 音频参数
- **编码格式：** AAC
- **比特率：** 128kbps
- **采样率：** 44.1kHz
- **共享模式：** 所有摄像头共享同一音频源

## 🛠️ 系统维护

### 维护工具使用
```bash
# 必须以root用户身份运行
sudo ./板子/劝导站智能推流/system-maintenance.sh
```

**功能菜单：**
```
📹 摄像头管理：
  1. 查看摄像头状态
  2. 添加摄像头
  3. 删除摄像头
  4. 重启推流服务

🎤 音频诊断：
  5. 快速音频诊断
  6. 测试音频功能
  7. 检查设备冲突
  8. 修复音频问题

🔧 系统维护：
  9. 查看系统状态
  10. 查看服务日志
  11. 重启所有服务
```

### 常用管理命令
```bash
# 查看推流服务状态
sudo systemctl status multi-camera-push

# 重启推流服务
sudo systemctl restart multi-camera-push

# 查看实时日志
tail -f /home/<USER>/srs/logs/multi-camera-push.log

# 检查音频设备
arecord -l
pactl list sources short

# 检查WebRTC进程
ps aux | grep chromium
```

## 📋 配置文件说明

### 主配置文件
**位置：** `/home/<USER>/srs/camera_config.json`

**结构：**
```json
{
    "srs_server": "服务器地址:端口",
    "backend_url": "后端API地址",
    "audio_config": {
        "enabled": true,
        "device": "音频设备",
        "use_pulseaudio": true,
        "virtual_source": "countryside_audio_source"
    },
    "cameras": [
        {
            "name": "摄像头名称",
            "ip": "摄像头IP",
            "stream_name": "流名称",
            "rtsp_url": "RTSP地址"
        }
    ]
}
```

### 服务配置
**systemd服务：** `/etc/systemd/system/multi-camera-push.service`
**WebRTC自启动：** `/home/<USER>/.config/autostart/webrtc-auto.desktop`

## 🔍 故障排除

### 常见问题

**1. 推流服务启动失败**
```bash
# 查看详细错误
sudo systemctl status multi-camera-push
tail -n 20 /home/<USER>/srs/logs/multi-camera-push.log

# 重新部署
sudo ./smart-stream-deploy.sh
```

**2. 音频设备冲突**
```bash
# 使用维护工具诊断
sudo ./system-maintenance.sh
# 选择 "5. 快速音频诊断"

# 手动修复
pulseaudio --kill
pulseaudio --start
```

**3. WebRTC无法连接**
```bash
# 检查WebRTC进程
ps aux | grep chromium

# 重启WebRTC（以orangepi用户身份）
su - orangepi
pkill chromium
/home/<USER>/srs/auto_request.sh &

# 查看WebRTC日志
tail -f /home/<USER>/srs/logs/webrtc.log
```

**4. 摄像头连接失败**
```bash
# 测试RTSP连接
ffmpeg -i "rtsp://用户名:密码@IP/LiveMedia/ch1/Media2" -t 5 -f null -

# 检查网络连接
ping 摄像头IP
```

### 日志管理

**日志自动清理配置：**
- **推流服务日志**：自动保留7天，每天轮转压缩
- **WebRTC服务日志**：自动保留7天，每天轮转压缩
- **清理配置文件**：
  - `/etc/logrotate.d/multi-camera-push`
  - `/etc/logrotate.d/webrtc-logs`

**推流服务日志：**
```bash
# 实时查看
tail -f /home/<USER>/srs/logs/multi-camera-push.log

# 查看错误
grep "ERROR" /home/<USER>/srs/logs/multi-camera-push.log

# 查看今天的日志
grep "$(date +%Y-%m-%d)" /home/<USER>/srs/logs/multi-camera-push.log

# 强制轮转日志
sudo logrotate -f /etc/logrotate.d/multi-camera-push
```

**WebRTC服务日志：**
```bash
# 实时查看
tail -f /home/<USER>/srs/logs/webrtc.log

# 查看错误
grep "ERROR" /home/<USER>/srs/logs/webrtc.log

# 查看浏览器启动记录
grep "启动浏览器" /home/<USER>/srs/logs/webrtc.log

# 强制轮转日志
sudo logrotate -f /etc/logrotate.d/webrtc-logs
```

**系统音频日志：**
```bash
# 查看音频设备日志
dmesg | grep -i audio

# 查看PulseAudio日志
journalctl -u pulseaudio --since today
```

## 📊 性能监控

### 资源使用情况
```bash
# CPU和内存使用
top -p $(pgrep -f multi_camera_push)

# 网络使用
iftop -i eth0

# 磁盘使用
df -h /home/<USER>/srs/
```

### 性能优化建议
- **CPU使用率：** 音频编码增加5-10%CPU使用
- **内存使用：** 每个音频流增加10-20MB内存
- **网络带宽：** 每个流增加128kbps上行带宽
- **存储空间：** 定期清理90天前的录制文件

## 🔧 高级配置

### 自定义音频参数
修改 `smart-stream-deploy.sh` 中的FFmpeg参数：
```bash
# 调整音频比特率
'-b:a', '64k'    # 降低比特率节省带宽
'-b:a', '256k'   # 提高比特率改善音质

# 调整采样率
'-ar', '22050'   # 降低采样率
'-ar', '48000'   # 提高采样率
```

### 多网卡配置
如果系统有多个网卡，可以指定推流使用的网卡：
```bash
# 在FFmpeg命令中添加
'-f', 'flv', '-bind_address', '*************', rtmp_url
```

### 安全配置
```bash
# 设置防火墙规则
sudo ufw allow from ***********/16 to any port 1935
sudo ufw allow from ***********/16 to any port 8080

# 限制SSH访问
sudo ufw allow from ***********/16 to any port 22
```

## 📞 技术支持

### 问题反馈
如遇到问题，请提供以下信息：
1. 系统版本和硬件配置
2. 错误日志和测试结果
3. 网络环境和设备信息
4. 具体的操作步骤和现象

### 维护建议
- 定期检查系统状态（建议每周一次）
- 及时清理日志文件和录制文件
- 监控网络带宽和系统资源使用
- 备份重要的配置文件

### 更新升级
```bash
# 备份配置
cp /home/<USER>/srs/camera_config.json ~/backup/

# 重新部署（保留配置）
sudo ./smart-stream-deploy.sh

# 验证功能
sudo ./system-maintenance.sh
```

---

**版本信息：** v2.0 - 支持音频采集和PulseAudio虚拟化  
**更新日期：** 2024年  
**适用系统：** Ubuntu/Debian Linux
