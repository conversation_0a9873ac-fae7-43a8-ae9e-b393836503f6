#!/bin/bash
# 农村两轮车监控系统 - 智能推流一键部署脚本
# 解决前端关闭视频后推流还在继续的问题

set -e

echo "🎬 农村两轮车监控系统 - 智能推流一键部署"
echo "=============================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo运行此脚本: sudo ./smart-stream-deploy.sh"
    exit 1
fi


# 检查必要依赖
echo "🔍 检查必要依赖..."

if ! command -v python3 >/dev/null 2>&1; then
    echo "❌ 缺少Python3，请先安装: sudo apt install python3"
    exit 1
fi

if ! command -v ffmpeg >/dev/null 2>&1; then
    echo "❌ 缺少FFmpeg，请先安装: sudo apt install ffmpeg"
    exit 1
fi

if ! command -v arecord >/dev/null 2>&1; then
    echo "❌ 缺少ALSA工具，请先安装: sudo apt install alsa-utils"
    exit 1
fi

if ! command -v pulseaudio >/dev/null 2>&1; then
    echo "❌ 缺少PulseAudio，请先安装: sudo apt install pulseaudio pulseaudio-utils"
    exit 1
fi

echo "✅ Python3、FFmpeg、ALSA和PulseAudio已安装"

# 检测音频设备
echo "🎤 检测音频设备..."
AUDIO_DEVICE=""
AUDIO_ENABLED=false
USE_PULSEAUDIO=false

# 检查是否有音频录制设备
if arecord -l 2>/dev/null | grep -q "card"; then
    echo "📋 可用的音频录制设备:"
    arecord -l | grep "card"
    echo ""

    # 自动选择第一个可用设备
    FIRST_CARD=$(arecord -l 2>/dev/null | grep "card" | head -1 | sed 's/card \([0-9]\+\).*/\1/')
    if [ -n "$FIRST_CARD" ]; then
        AUDIO_DEVICE="hw:$FIRST_CARD,0"
        echo "🎵 检测到音频设备: $AUDIO_DEVICE"

        read -p "🔧 是否启用音频采集？(Y/n): " ENABLE_AUDIO
        if [[ ! $ENABLE_AUDIO =~ ^[Nn]$ ]]; then
            AUDIO_ENABLED=true
            echo "✅ 音频采集已启用"

            # 询问是否配置PulseAudio虚拟化
            echo ""
            echo "🔄 音频共享配置选项:"
            echo "   1. 直接使用ALSA设备（可能与WebRTC冲突）"
            echo "   2. 配置PulseAudio虚拟化（推荐，支持多应用共享）"
            echo ""
            read -p "🔧 选择配置方式 (1/2) [2]: " AUDIO_MODE
            AUDIO_MODE=${AUDIO_MODE:-2}

            if [ "$AUDIO_MODE" = "2" ]; then
                USE_PULSEAUDIO=true
                echo "🔄 将配置PulseAudio虚拟音频源"

                # 配置PulseAudio虚拟源
                echo "⚙️ 配置PulseAudio虚拟音频源..."

                # 确保PulseAudio运行
                if ! pgrep -x "pulseaudio" > /dev/null; then
                    echo "🚀 启动PulseAudio..."
                    pulseaudio --start --log-target=syslog 2>/dev/null || true
                    sleep 2
                fi

                # 创建虚拟音频源
                VIRTUAL_SOURCE="countryside_audio_source"
                if pactl list sources short | grep -q "$VIRTUAL_SOURCE"; then
                    echo "🔄 虚拟音频源已存在，重新创建..."
                    pactl unload-module module-virtual-source 2>/dev/null || true
                fi

                # 加载虚拟音频源模块
                if pactl load-module module-virtual-source source_name="$VIRTUAL_SOURCE" master="alsa_input.hw_${FIRST_CARD}_0" 2>/dev/null; then
                    echo "✅ PulseAudio虚拟音频源创建成功"
                    AUDIO_DEVICE="pulse:$VIRTUAL_SOURCE"

                    # 测试虚拟音频源
                    echo "🧪 测试虚拟音频源..."
                    if timeout 3 ffmpeg -f pulse -i "$VIRTUAL_SOURCE" -f null - 2>/dev/null; then
                        echo "✅ 虚拟音频源测试成功"
                    else
                        echo "⚠️  虚拟音频源测试失败，回退到ALSA设备"
                        USE_PULSEAUDIO=false
                        AUDIO_DEVICE="hw:$FIRST_CARD,0"
                    fi
                else
                    echo "❌ PulseAudio虚拟音频源创建失败，使用ALSA设备"
                    USE_PULSEAUDIO=false
                    AUDIO_DEVICE="hw:$FIRST_CARD,0"
                fi
            else
                echo "📝 使用直接ALSA设备模式"
                # 测试ALSA设备
                echo "🧪 测试ALSA音频设备..."
                if timeout 3 arecord -D "$AUDIO_DEVICE" -f cd -t wav /dev/null 2>/dev/null; then
                    echo "✅ ALSA音频设备测试成功"
                else
                    echo "⚠️  ALSA音频设备测试失败，但将继续使用"
                fi
            fi
        else
            echo "⏭️  跳过音频采集"
        fi
    fi
else
    echo "⚠️  未检测到音频录制设备，将仅推送视频流"
fi

# 检查Python依赖
if ! command -v pip3 >/dev/null 2>&1; then
    echo "❌ 缺少 pip3，请先安装: sudo apt install python3-pip"
    exit 1
fi

if ! python3 -c "import requests" 2>/dev/null; then
    echo "📦 安装Python requests库..."
    pip3 install requests
else
    echo "✅ Python requests库已安装"
fi

# 获取摄像头数量
read -p "🔧 请输入要配置的摄像头数量 [2]: " CAMERA_COUNT
CAMERA_COUNT=${CAMERA_COUNT:-2}

if ! [[ "$CAMERA_COUNT" =~ ^[0-9]+$ ]] || [ "$CAMERA_COUNT" -lt 1 ] || [ "$CAMERA_COUNT" -gt 8 ]; then
    echo "❌ 摄像头数量必须是1-8之间的数字"
    exit 1
fi

# 获取SRS和后端API地址
read -p "🔧 请输入SRS服务器地址 [***************:1935]: " SRS_SERVER
SRS_SERVER=${SRS_SERVER:-***************:1935}

read -p "🔧 请输入后端API地址 [http://***************:8081]: " BACKEND_URL
BACKEND_URL=${BACKEND_URL:-http://***************:8081}

# 收集摄像头配置
declare -a CAMERA_CONFIGS
for ((i=1; i<=CAMERA_COUNT; i++)); do
    echo ""
    echo "=== 配置摄像头 $i ==="

    read -p "🔧 摄像头${i}名称 [device1$(($i+1))0]: " CAM_NAME
    CAM_NAME=${CAM_NAME:-device1$(($i+1))0}

    read -p "🔧 摄像头${i}IP地址 [***********$(($i+1))0]: " CAM_IP
    CAM_IP=${CAM_IP:-***********$(($i+1))0}

    read -p "🔧 摄像头${i}用户名 [admin]: " CAM_USER
    CAM_USER=${CAM_USER:-admin}

    read -p "🔧 摄像头${i}密码 [ybda2025]: " CAM_PASS
    CAM_PASS=${CAM_PASS:-ybda2025}

    read -p "🔧 流名称 [stream-${CAM_NAME}]: " STREAM_NAME
    STREAM_NAME=${STREAM_NAME:-stream-${CAM_NAME}}

    DEFAULT_RTSP="rtsp://${CAM_USER}:${CAM_PASS}@${CAM_IP}/LiveMedia/ch1/Media2"
    read -p "🔧 RTSP地址 [$DEFAULT_RTSP]: " RTSP_URL
    RTSP_URL=${RTSP_URL:-$DEFAULT_RTSP}

    # 音频源选择
    echo ""
    echo "🎤 音频源配置 (摄像头${i}):"
    echo "   1. 摄像机内置麦克风（RTSP音频）"
    echo "   2. USB硬件麦克风（环境音频）"
    echo "   3. 禁用音频"
    echo ""
    read -p "🔧 请选择音频源 (1/2/3) [2]: " AUDIO_SOURCE
    AUDIO_SOURCE=${AUDIO_SOURCE:-2}

    case $AUDIO_SOURCE in
        1)
            AUDIO_MODE="camera"
            echo "✅ 已选择：摄像机内置麦克风"
            ;;
        2)
            AUDIO_MODE="usb"
            echo "✅ 已选择：USB硬件麦克风"
            ;;
        3)
            AUDIO_MODE="none"
            echo "✅ 已选择：禁用音频"
            ;;
        *)
            AUDIO_MODE="usb"
            echo "✅ 默认选择：USB硬件麦克风"
            ;;
    esac

    CAMERA_CONFIGS[$i]="$CAM_NAME|$CAM_IP|$STREAM_NAME|$RTSP_URL|$AUDIO_MODE"
done

# 确认配置
echo ""
echo "📝 配置信息确认:"
echo "   SRS服务器: $SRS_SERVER"
echo "   后端API: $BACKEND_URL"
for ((i=1; i<=CAMERA_COUNT; i++)); do
    IFS='|' read -r cam_name cam_ip stream_name rtsp_url audio_mode <<< "${CAMERA_CONFIGS[$i]}"

    # 显示音频模式
    case $audio_mode in
        "camera") audio_desc="摄像机内置麦克风" ;;
        "usb") audio_desc="USB硬件麦克风" ;;
        "none") audio_desc="禁用音频" ;;
        *) audio_desc="未知" ;;
    esac

    echo "   摄像头$i: $cam_name ($cam_ip) -> $stream_name"
    echo "           音频源: $audio_desc"
done
echo ""

read -p "✅ 确认配置正确？(y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "❌ 部署取消"
    exit 1
fi

# 跳过RTSP连接测试，直接部署
echo ""
echo "📝 注意: 已跳过RTSP连接测试，如有连接问题请查看运行日志"
echo ""

# 创建目录
BASE_DIR="/home/<USER>/srs"
mkdir -p "$BASE_DIR/logs"

# 停止现有服务
systemctl stop multi-camera-push 2>/dev/null || true

# 创建智能推流脚本
echo "📝 创建智能推流脚本..."
cat > "$BASE_DIR/multi_camera_push.py" << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农村两轮车监控系统 - 智能推流脚本
解决前端关闭视频后推流还在继续的问题
"""

import json
import subprocess
import threading
import time
import sys
import os
import signal
import requests
import logging
from datetime import datetime

class SmartCameraStreamer:
    def __init__(self, camera_info, srs_server, backend_url, logger, audio_config=None):
        self.camera_info = camera_info
        self.srs_server = srs_server
        self.backend_url = backend_url
        self.logger = logger
        self.audio_config = audio_config  # 音频配置
        self.process = None
        self.is_running = False
        self.is_stopped = False
        self.consecutive_failures = 0
        self.current_status = "待机中"
        self.last_status = None  # 用于检测状态变化
        self.viewer_check_running = False
        self.last_log_time = 0  # 上次记录日志的时间
        
    def log(self, message, level="INFO"):
        """统一日志输出格式"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        camera_name = self.camera_info['name']
        
        icons = {"INFO": "📡", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}
        icon = icons.get(level, "📡")
        
        log_message = f"[{timestamp}] {icon} {camera_name}: {message}"
        
        # 输出到控制台
        print(log_message)
        
        # 写入文件日志
        if level == "ERROR":
            self.logger.error(f"{camera_name}: {message}")
        elif level == "WARNING":
            self.logger.warning(f"{camera_name}: {message}")
        elif level == "SUCCESS":
            self.logger.info(f"{camera_name}: {message}")
        else:
            self.logger.info(f"{camera_name}: {message}")
    
    def check_viewer_status(self):
        """检查是否有观看者"""
        try:
            response = requests.post(
                f"{self.backend_url}/api/on_publish",
                json={
                    "stream": self.camera_info['stream_name'],
                    "app": "live",
                    "client_id": "smart_client"
                },
                timeout=3
            )
            return response.status_code == 200
        except:
            return False
    
    def continuous_viewer_check(self):
        """推流期间持续检测观看者状态"""
        self.viewer_check_running = True
        while self.viewer_check_running and self.is_running:
            try:
                time.sleep(5)  # 每5秒检查一次
                if not self.viewer_check_running or not self.is_running:
                    break
                has_viewer = self.check_viewer_status()
                if not has_viewer:
                    self.log("检测到观看者已离开，停止推流", "WARNING")
                    if self.process:
                        self.process.terminate()
                    # 重置状态，让主循环能正确检测状态变化
                    self.last_status = None
                    break
            except:
                break
    
    def test_rtsp_connection(self):
        """使用FFmpeg测试RTSP连接"""
        try:
            # 使用FFmpeg测试RTSP连接（3秒测试）
            test_cmd = [
                'ffmpeg', '-i', self.camera_info['rtsp_url'],
                '-t', '3',  # 只测试3秒
                '-f', 'null', '/dev/null',  # 输出到null，不保存文件
                '-v', 'quiet'  # 静默模式，减少输出
            ]

            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log("RTSP连接测试成功", "SUCCESS")
                return True
            else:
                # 检查是否是正常的流结束（不是错误）
                if "time=" in result.stderr or len(result.stderr.strip()) == 0:
                    self.log("RTSP连接测试成功", "SUCCESS")
                    return True
                else:
                    self.log(f"RTSP连接测试失败: {result.stderr.strip()}", "ERROR")
                    return False
        except subprocess.TimeoutExpired:
            self.log("RTSP连接测试超时", "ERROR")
            return False
        except Exception as e:
            self.log(f"RTSP连接测试异常: {e}", "ERROR")
            return False

    def start_ffmpeg_stream(self):
        """启动FFmpeg推流进程"""
        rtmp_url = f"rtmp://{self.srs_server}/live/{self.camera_info['stream_name']}"

        # 跳过RTSP连接测试，直接推流（提高响应速度）

        # 根据摄像头的音频模式构建FFmpeg命令
        audio_mode = self.camera_info.get('audio_mode', 'none')

        if audio_mode == 'camera':
            # 使用摄像机内置麦克风（RTSP音频）
            self.log("使用摄像机内置麦克风", "INFO")
            cmd = [
                'ffmpeg', '-i', self.camera_info['rtsp_url'],
                '-c', 'copy',  # 直接复制视频和音频流
                '-f', 'flv', rtmp_url, '-y',
                '-loglevel', 'error', '-timeout', '10000000'
            ]

        elif audio_mode == 'usb' and self.audio_config and self.audio_config.get('enabled', False):
            # 使用USB硬件麦克风
            audio_device = self.audio_config.get('device', 'hw:0,0')
            use_pulseaudio = self.audio_config.get('use_pulseaudio', False)

            if use_pulseaudio:
                # 使用PulseAudio虚拟源
                virtual_source = self.audio_config.get('virtual_source', 'countryside_audio_source')
                self.log(f"使用USB麦克风（PulseAudio），虚拟源: {virtual_source}", "INFO")

                cmd = [
                    'ffmpeg',
                    '-i', self.camera_info['rtsp_url'],  # 视频输入
                    '-f', 'pulse', '-i', virtual_source, # PulseAudio音频输入
                    '-c:v', 'copy',                      # 视频直接复制
                    '-c:a', 'aac',                       # 音频编码为AAC
                    '-b:a', '128k',                      # 音频比特率
                    '-ar', '44100',                      # 音频采样率
                    '-map', '0:v:0',                     # 映射视频流
                    '-map', '1:a:0',                     # 映射音频流
                    '-f', 'flv', rtmp_url, '-y',
                    '-loglevel', 'error', '-timeout', '10000000'
                ]
            else:
                # 使用ALSA设备
                self.log(f"使用USB麦克风（ALSA），设备: {audio_device}", "INFO")

                cmd = [
                    'ffmpeg',
                    '-i', self.camera_info['rtsp_url'],  # 视频输入
                    '-f', 'alsa', '-i', audio_device,    # ALSA音频输入
                    '-c:v', 'copy',                      # 视频直接复制
                    '-c:a', 'aac',                       # 音频编码为AAC
                    '-b:a', '128k',                      # 音频比特率
                    '-ar', '44100',                      # 音频采样率
                    '-map', '0:v:0',                     # 映射视频流
                    '-map', '1:a:0',                     # 映射音频流
                    '-f', 'flv', rtmp_url, '-y',
                    '-loglevel', 'error', '-timeout', '10000000'
                ]
        else:
            # 禁用音频或无音频设备
            self.log("仅推送视频流（无音频）", "INFO")
            cmd = [
                'ffmpeg', '-i', self.camera_info['rtsp_url'],
                '-c:v', 'copy',  # 仅复制视频流
                '-an',           # 禁用音频
                '-f', 'flv', rtmp_url, '-y',
                '-loglevel', 'error', '-timeout', '10000000'
            ]

        try:
            self.process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return True
        except Exception as e:
            self.log(f"启动FFmpeg失败: {e}", "ERROR")
            return False
    
    def run_stream_loop(self):
        """主推流循环"""
        self.log("推流服务启动", "INFO")
        
        while not self.is_stopped:
            try:
                # 检查观看者
                has_viewer = self.check_viewer_status()
                
                if not has_viewer:
                    self.consecutive_failures += 1
                    new_status = "无观看者，待机中"
                    
                    # 仅在状态变化时记录日志
                    if self.last_status != new_status:
                        self.log("无观看者，进入待机状态", "INFO")
                        self.current_status = new_status
                        self.last_status = new_status
                    
                    time.sleep(5)  # 固定5秒检测间隔
                    continue
                
                # 有观看者，开始推流
                new_status = "推流中"
                if self.last_status != new_status:
                    self.log("检测到观看者，开始推流", "SUCCESS")
                    self.consecutive_failures = 0
                    self.current_status = new_status
                    self.last_status = new_status
                
                if not self.start_ffmpeg_stream():
                    time.sleep(10)
                    continue
                
                self.is_running = True
                self.log("推流进程已启动，开始持续监控", "SUCCESS")
                
                # 启动持续检测线程
                check_thread = threading.Thread(target=self.continuous_viewer_check, daemon=True)
                check_thread.start()
                
                # 等待推流结束
                return_code = self.process.wait()
                self.viewer_check_running = False
                
                if return_code == 0:
                    self.log("推流正常结束", "SUCCESS")
                else:
                    self.log("推流异常结束，观看者可能已离开", "INFO")
                
                # 重置状态，让下次循环能正确检测状态变化
                self.last_status = None
                
            except Exception as e:
                self.log(f"推流循环异常: {e}", "ERROR")
                time.sleep(10)
            finally:
                self.cleanup_process()
                self.current_status = "待机中"
                self.last_status = None  # 重置状态检测
    
    def cleanup_process(self):
        """清理推流进程"""
        self.viewer_check_running = False
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=3)
            except:
                try:
                    self.process.kill()
                except:
                    pass
            finally:
                self.process = None
                self.is_running = False
    
    def stop(self):
        """停止推流"""
        self.is_stopped = True
        self.cleanup_process()

class SmartMultiCameraPusher:
    def __init__(self, config_file):
        self.config = self.load_config(config_file)
        self.logger = self.setup_logger()
        self.streamers = []
        self.threads = []
        self.running = True
        
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logger(self):
        """设置日志系统"""
        logger = logging.getLogger('multi_camera_push')
        logger.setLevel(logging.INFO)
        
        # 如果已经有handler，不重复添加
        if not logger.handlers:
            # 创建文件处理器
            log_file = '/home/<USER>/srs/logs/multi-camera-push.log'
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 设置日志格式
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            
            # 添加处理器
            logger.addHandler(file_handler)
        
        return logger
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置加载成功: {len(config['cameras'])} 个摄像头")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            sys.exit(1)
    
    def test_backend_connection(self):
        """测试后端API连接"""
        try:
            response = requests.get(f"{self.config['backend_url']}/api/stream/status", timeout=5)
            if response.status_code == 200:
                print(f"✅ 后端API连接正常: {self.config['backend_url']}")
                return True
            else:
                print(f"⚠️ 后端API响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 后端API连接失败: {e}")
            return False
    
    def start_all_cameras(self):
        """启动所有摄像头推流"""
        print(f"🚀 启动 {len(self.config['cameras'])} 个摄像头推流...")

        # 获取音频配置
        audio_config = self.config.get('audio_config', {'enabled': False})
        if audio_config.get('enabled', False):
            if audio_config.get('use_pulseaudio', False):
                print(f"🎤 音频采集已启用（PulseAudio），虚拟源: {audio_config.get('virtual_source', 'countryside_audio_source')}")
                print("🔄 支持多应用音频共享，与WebRTC无冲突")
            else:
                print(f"🎤 音频采集已启用（ALSA），设备: {audio_config.get('device', 'hw:0,0')}")
                print("⚠️  直接ALSA模式，可能与WebRTC冲突")
        else:
            print("📹 仅推送视频流（无音频）")

        for camera in self.config['cameras']:
            print(f"启动摄像头: {camera['name']} (IP: {camera['ip']})")

            streamer = SmartCameraStreamer(camera, self.config['srs_server'], self.config['backend_url'], self.logger, audio_config)
            self.streamers.append(streamer)

            thread = threading.Thread(target=streamer.run_stream_loop, name=f"Camera-{camera['name']}")
            thread.daemon = True
            self.threads.append(thread)
            thread.start()

            time.sleep(2)

        print("✅ 所有摄像头推流线程已启动")
    
    def show_status(self):
        """显示系统状态"""
        last_status_summary = None
        while self.running:
            time.sleep(300)  # 改为5分钟显示一次
            
            # 生成状态摘要
            status_summary = []
            for streamer in self.streamers:
                status_summary.append(f"{streamer.camera_info['name']}:{streamer.current_status}")
            
            current_summary = "|".join(status_summary)
            
            # 仅在状态变化时显示
            if current_summary != last_status_summary:
                print(f"\n📊 系统状态 [{datetime.now().strftime('%H:%M:%S')}]")
                for streamer in self.streamers:
                    status_icon = "🟢" if streamer.is_running else "🔴"
                    print(f"{status_icon} {streamer.camera_info['name']}: {streamer.current_status}")
                print()
                last_status_summary = current_summary
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止服务...")
        self.running = False
        for streamer in self.streamers:
            streamer.stop()
    
    def run(self):
        """主运行函数"""
        print("🎬 农村两轮车监控系统 - 智能推流服务")
        print(f"📡 SRS服务器: {self.config['srs_server']}")
        print("🔧 功能特点: 按需推流 + 持续监控 + 智能重试 + 简洁日志")
        print("🆕 解决问题: 前端关闭视频后5秒内停止推流")
        print("-" * 60)
        
        if not self.test_backend_connection():
            print("⚠️ 后端API连接失败，但继续尝试推流...")
        
        self.start_all_cameras()
        
        status_thread = threading.Thread(target=self.show_status, daemon=True)
        status_thread.start()
        
        print("📝 智能日志: 仅在状态变化时显示  📡 信息  ✅ 成功  ⚠️ 警告  ❌ 错误")
        print("⏹️ 按 Ctrl+C 停止推流")
        print("-" * 60)
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n收到中断信号...")
        
        for streamer in self.streamers:
            streamer.stop()
        
        print("🎬 智能推流服务已停止")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python3 multi_camera_push.py <配置文件>")
        sys.exit(1)
    
    config_file = sys.argv[1]
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        sys.exit(1)
    
    pusher = SmartMultiCameraPusher(config_file)
    pusher.run()

if __name__ == '__main__':
    main()
EOF

# 创建配置文件
echo "📝 生成配置文件..."
cat > "$BASE_DIR/camera_config.json" << EOF
{
    "srs_server": "$SRS_SERVER",
    "backend_url": "$BACKEND_URL",
    "audio_config": {
        "enabled": $AUDIO_ENABLED,
        "device": "$AUDIO_DEVICE",
        "use_pulseaudio": $USE_PULSEAUDIO,
        "virtual_source": "countryside_audio_source"
    },
    "cameras": [
EOF

for ((i=1; i<=CAMERA_COUNT; i++)); do
    IFS='|' read -r cam_name cam_ip stream_name rtsp_url audio_mode <<< "${CAMERA_CONFIGS[$i]}"

    if [ $i -gt 1 ]; then
        echo "," >> "$BASE_DIR/camera_config.json"
    fi

    cat >> "$BASE_DIR/camera_config.json" << EOF
        {
            "name": "$cam_name",
            "ip": "$cam_ip",
            "stream_name": "$stream_name",
            "rtsp_url": "$rtsp_url",
            "audio_mode": "$audio_mode"
        }
EOF
done

cat >> "$BASE_DIR/camera_config.json" << EOF

    ]
}
EOF

# 设置权限
chmod +x "$BASE_DIR/multi_camera_push.py"
chown -R orangepi:orangepi "$BASE_DIR"

# 创建或更新systemd服务
echo "⚙️ 配置系统服务..."
cat > "/etc/systemd/system/multi-camera-push.service" << EOF
[Unit]
Description=农村两轮车监控智能推流服务
After=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=$BASE_DIR
ExecStart=/usr/bin/python3 $BASE_DIR/multi_camera_push.py $BASE_DIR/camera_config.json
Restart=always
RestartSec=10
StandardOutput=append:/home/<USER>/srs/logs/multi-camera-push.log
StandardError=append:/home/<USER>/srs/logs/multi-camera-push.log

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable multi-camera-push

# 配置日志轮转
echo "🧹 配置日志轮转（保留7天）..."
cat > /etc/logrotate.d/multi-camera-push << EOF
/home/<USER>/srs/logs/multi-camera-push.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 orangepi orangepi
    postrotate
        systemctl reload multi-camera-push 2>/dev/null || true
    endscript
}
EOF
echo "✅ 已配置日志轮转策略"

# 启动服务
echo "🚀 启动智能推流服务..."
systemctl start multi-camera-push

sleep 3

if systemctl is-active --quiet multi-camera-push; then
    echo "🎉 智能推流服务启动成功！"
    echo ""
    echo "📊 服务状态:"
    systemctl status multi-camera-push --no-pager -l | head -10
    echo ""
    echo "📋 管理命令:"
    echo "   查看实时日志: tail -f /home/<USER>/srs/logs/multi-camera-push.log"
    echo "   查看最近日志: tail -n 50 /home/<USER>/srs/logs/multi-camera-push.log"
    echo "   重启服务:     sudo systemctl restart multi-camera-push"
    echo "   停止服务:     sudo systemctl stop multi-camera-push"
    echo "   清理旧日志:   sudo logrotate -f /etc/logrotate.d/multi-camera-push"
    echo ""
    echo "🎮 推流地址:"
    for ((i=1; i<=CAMERA_COUNT; i++)); do
        IFS='|' read -r cam_name cam_ip stream_name rtsp_url <<< "${CAMERA_CONFIGS[$i]}"
        echo "   $cam_name: rtmp://$SRS_SERVER/live/$stream_name"
    done
    echo ""

    # 显示音频配置信息
    if [ "$AUDIO_ENABLED" = "true" ]; then
        echo "🎤 音频配置:"
        echo "   音频采集: 已启用"
        if [ "$USE_PULSEAUDIO" = "true" ]; then
            echo "   音频模式: PulseAudio虚拟化"
            echo "   虚拟音频源: countryside_audio_source"
            echo "   设备冲突: ✅ 无冲突，支持与WebRTC同时使用"
        else
            echo "   音频模式: 直接ALSA设备"
            echo "   音频设备: $AUDIO_DEVICE"
            echo "   设备冲突: ⚠️  可能与WebRTC冲突"
        fi
        echo "   音频编码: AAC 128kbps 44.1kHz"
        echo "   共享模式: 所有摄像头共享同一音频源"
        echo ""
    else
        echo "📹 音频配置: 仅推送视频流（无音频）"
        echo ""
    fi

    echo "✅ 功能特性:"
    echo "   ✓ 前端关闭视频后推流会在5秒内停止"
    echo "   ✓ 智能观看者检测和推流控制"
    if [ "$AUDIO_ENABLED" = "true" ]; then
        echo "   ✓ 环境音频采集和同步推送"
        if [ "$USE_PULSEAUDIO" = "true" ]; then
            echo "   ✓ PulseAudio虚拟化，支持多应用音频共享"
            echo "   ✓ 与WebRTC语音通话无冲突"
        fi
    fi
else
    echo "❌ 服务启动失败，查看错误日志:"
    tail -n 20 /home/<USER>/srs/logs/multi-camera-push.log 2>/dev/null || echo "日志文件还未创建"
fi 