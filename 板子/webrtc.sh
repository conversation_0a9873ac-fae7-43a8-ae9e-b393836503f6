#!/bin/bash

# WebRTC语音通话自动启动脚本
# 支持PulseAudio虚拟化，避免与推流音频冲突

# 定义变量
USER_HOME="/home/<USER>"
CONFIG_DIR="$USER_HOME/.config/autostart"
WEBRTC_DIR="$USER_HOME/srs"
DESKTOP_FILE="$CONFIG_DIR/webrtc-auto.desktop"
SCRIPT_FILE="$WEBRTC_DIR/auto_request.sh"
VIRTUAL_SOURCE="countryside_audio_source"

# 检查PulseAudio和虚拟音频源
echo "🔊 检查音频环境..."

# 检查PulseAudio是否安装
if ! command -v pulseaudio >/dev/null 2>&1; then
    echo "❌ PulseAudio未安装，请先安装: sudo apt install pulseaudio pulseaudio-utils"
    exit 1
fi

if ! command -v pactl >/dev/null 2>&1; then
    echo "❌ PulseAudio控制工具未安装，请先安装: sudo apt install pulseaudio-utils"
    exit 1
fi

# 确保PulseAudio运行
if ! pgrep -x "pulseaudio" > /dev/null; then
    echo "🚀 启动PulseAudio..."
    pulseaudio --start --log-target=syslog 2>/dev/null || true
    sleep 2
fi

# 检查虚拟音频源是否存在
if pactl list sources short | grep -q "$VIRTUAL_SOURCE"; then
    echo "✅ 检测到虚拟音频源: $VIRTUAL_SOURCE"
    USE_VIRTUAL_AUDIO=true
else
    echo "⚠️  未检测到虚拟音频源，将使用默认音频设备"
    echo "💡 提示: 运行 smart-stream-deploy.sh 可配置虚拟音频源"
    USE_VIRTUAL_AUDIO=false
fi

# 环境依赖检查
echo ""
echo "🔍 检查环境依赖..."

# 检查curl
if ! command -v curl >/dev/null 2>&1; then
    echo "❌ 缺少curl，请先安装: sudo apt install curl"
    exit 1
fi

# 检查浏览器
if command -v chromium-browser >/dev/null 2>&1; then
    echo "✅ 检测到浏览器: chromium-browser"
elif command -v firefox >/dev/null 2>&1; then
    echo "✅ 检测到浏览器: firefox"
else
    echo "❌ 未找到浏览器，请先安装: sudo apt install chromium-browser"
    exit 1
fi

echo "✅ 环境依赖检查完成"

# 交互式配置
echo ""
echo "🔧 WebRTC配置"
echo "=============="

# 获取房间ID
read -p "🔧 请输入WebRTC房间ID [123456]: " ROOM_ID
ROOM_ID=${ROOM_ID:-123456}

# 获取后端API地址
read -p "🔧 请输入后端API地址 [http://117.175.127.248:8081]: " BACKEND_API
BACKEND_API=${BACKEND_API:-http://117.175.127.248:8081}

# 获取成功页面地址
read -p "🔧 请输入WebRTC页面地址 [https://countryside.yibindaoan.com/simple-voice-call.html]: " SUCCESS_PAGE_BASE
SUCCESS_PAGE_BASE=${SUCCESS_PAGE_BASE:-https://countryside.yibindaoan.com/simple-voice-call.html}

# 确认配置
echo ""
echo "📝 配置信息确认:"
echo "   房间ID: $ROOM_ID"
echo "   后端API: $BACKEND_API"
echo "   页面地址: $SUCCESS_PAGE_BASE"
echo ""

read -p "✅ 确认配置正确？(y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "❌ 配置取消"
    exit 1
fi

# 创建必要的目录结构
mkdir -p $CONFIG_DIR
mkdir -p $WEBRTC_DIR

# 创建自启动配置文件内容
cat <<EOL > $DESKTOP_FILE
[Desktop Entry]
Type=Application
Name=WebRTC Auto Start
Comment=Auto start WebRTC script
Exec=$WEBRTC_DIR/auto_request.sh
Terminal=false
X-GNOME-Autostart-enabled=true
EOL

# 设置配置文件权限
chmod 644 $DESKTOP_FILE
chown orangepi:orangepi $DESKTOP_FILE

# 创建脚本文件内容
cat <<EOL > $SCRIPT_FILE
#!/bin/bash

# 设置环境变量
export DISPLAY=:0
export XAUTHORITY=/home/<USER>/.Xauthority
export HOME=/home/<USER>
export DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus

# 日志配置
LOG_FILE="/home/<USER>/srs/logs/webrtc.log"
mkdir -p "\$(dirname "\$LOG_FILE")"

# 日志函数
log_info() {
    echo "\$(date '+%Y-%m-%d %H:%M:%S') [INFO] \$1" | tee -a "\$LOG_FILE"
}

log_error() {
    echo "\$(date '+%Y-%m-%d %H:%M:%S') [ERROR] \$1" | tee -a "\$LOG_FILE"
}

log_debug() {
    echo "\$(date '+%Y-%m-%d %H:%M:%S') [DEBUG] \$1" >> "\$LOG_FILE"
}

# 音频配置
VIRTUAL_SOURCE="$VIRTUAL_SOURCE"
USE_VIRTUAL_AUDIO=$USE_VIRTUAL_AUDIO

# WebRTC配置
ROOM_ID="$ROOM_ID"
BACKEND_API="$BACKEND_API"
SUCCESS_PAGE_BASE="$SUCCESS_PAGE_BASE"

# 启动日志
log_info "WebRTC自动监控服务启动"
log_info "房间ID: \$ROOM_ID"
log_info "后端API: \$BACKEND_API"
log_info "页面地址: \$SUCCESS_PAGE_BASE"

# 等待系统完全启动
log_info "等待系统完全启动..."
sleep 30
log_info "系统启动完成，开始监控"

# 配置PulseAudio音频环境
if [ "\$USE_VIRTUAL_AUDIO" = "true" ]; then
    log_info "配置虚拟音频环境..."
    # 确保PulseAudio运行
    if ! pgrep -x "pulseaudio" > /dev/null; then
        log_info "启动PulseAudio服务"
        pulseaudio --start --log-target=syslog 2>/dev/null || true
        sleep 2
    fi

    # 检查虚拟音频源是否存在
    if pactl list sources short | grep -q "\$VIRTUAL_SOURCE"; then
        # 设置虚拟音频源为默认输入设备
        pactl set-default-source "\$VIRTUAL_SOURCE" 2>/dev/null || true
        log_info "WebRTC使用虚拟音频源: \$VIRTUAL_SOURCE"
    else
        log_error "虚拟音频源不存在，切换到默认设备"
        USE_VIRTUAL_AUDIO=false
    fi
else
    log_info "使用默认音频设备"
fi

# 设置请求URL和成功后打开的页面URL
URL="\${BACKEND_API}/webrtc/requestWebRTC?roomId=\${ROOM_ID}"
SUCCESS_PAGE="\${SUCCESS_PAGE_BASE}?roomId=\${ROOM_ID}&autoJoin=true"

# 检查是否安装了curl
if ! command -v curl &> /dev/null; then
    log_error "curl未安装，请先安装: sudo apt-get install curl"
    exit 1
fi

# 设置浏览器命令
if command -v chromium-browser &> /dev/null; then
    BROWSER_CMD="chromium-browser"
    log_info "使用浏览器: chromium-browser"
elif command -v firefox &> /dev/null; then
    BROWSER_CMD="firefox"
    log_info "使用浏览器: firefox"
else
    log_error "未找到浏览器，请安装chromium-browser或firefox"
    log_error "可以运行: sudo apt-get install chromium-browser"
    exit 1
fi

# 函数：检查浏览器进程
check_browser() {
    # 检查是否有chromium进程且包含WebRTC相关参数
    if pgrep -f "chromium.*use-fake-ui-for-media-stream" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# 函数：启动浏览器
start_browser() {
    log_info "正在启动浏览器..."

    # 构建浏览器启动参数
    BROWSER_ARGS="--use-fake-ui-for-media-stream --enable-features=WebRTCPipeWireCapturer --no-sandbox --disable-dev-shm-usage"

    # 如果使用虚拟音频源，添加音频相关参数
    if [ "\$USE_VIRTUAL_AUDIO" = "true" ]; then
        # 添加PulseAudio相关参数
        BROWSER_ARGS="\$BROWSER_ARGS --enable-features=WebRTCPipeWireCapturer"
        BROWSER_ARGS="\$BROWSER_ARGS --autoplay-policy=no-user-gesture-required"

        # 设置音频设备环境变量
        export PULSE_RUNTIME_PATH="/run/user/1000/pulse"
        export PULSE_RUNTIME_PATH_FALLBACK="/tmp/pulse-\$(id -u)"

        log_info "浏览器将使用虚拟音频源"
    else
        log_info "浏览器将使用默认音频设备"
    fi

    log_debug "浏览器启动命令: \$BROWSER_CMD \$BROWSER_ARGS \$SUCCESS_PAGE"
    \$BROWSER_CMD \$BROWSER_ARGS "\$SUCCESS_PAGE" &
    log_info "浏览器已启动，PID: \$!"
}

# 函数：关闭浏览器
close_browser() {
    log_info "正在关闭浏览器..."
    if [ "\$BROWSER_CMD" = "chromium-browser" ]; then
        pkill chromium
        log_info "已关闭chromium浏览器"
    elif [ "\$BROWSER_CMD" = "firefox" ]; then
        pkill firefox
        log_info "已关闭firefox浏览器"
    fi
}

# 设置退出时的清理操作
trap close_browser EXIT

log_info "开始监控WebRTC房间状态..."
log_info "监控URL: \$URL"
log_info "按 Ctrl+C 可以停止脚本"

# 主循环
while true; do
    # 发送HTTP请求
    response=\$(curl -s "\$URL")
    code=\$(echo "\$response" | grep -o '"code":[0-9]*' | cut -d':' -f2)
    current_time=\$(date '+%Y-%m-%d %H:%M:%S')

    # 记录详细的API响应（仅记录到文件）
    log_debug "API响应: \$response"
    log_debug "状态码: \$code"

    # 检查浏览器是否在运行
    if ! check_browser; then
        log_debug "浏览器未运行"

        if [ "\$code" = "200" ]; then
            log_info "检测到房间可用(200)，正在启动浏览器..."
            start_browser
        else
            log_debug "等待房间可用... (当前状态: \$code)"
        fi
    else
        log_debug "浏览器运行中"

        if [ "\$code" = "500" ]; then
            log_info "检测到房间不可用(500)，关闭浏览器..."
            close_browser
        else
            log_debug "房间状态正常，继续监控 (状态: \$code)"
        fi
    fi

    # 每2秒检查一次
    sleep 2
done
EOL

# 设置脚本执行权限
chmod +x $SCRIPT_FILE
chown orangepi:orangepi $SCRIPT_FILE

echo "✅ WebRTC配置完成！"
echo ""
echo "📊 WebRTC配置信息:"
echo "   🏠 房间ID: $ROOM_ID"
echo "   🌐 后端API: $BACKEND_API"
echo "   📱 页面地址: $SUCCESS_PAGE_BASE"
echo ""
echo "📊 音频配置状态:"
if [ "$USE_VIRTUAL_AUDIO" = "true" ]; then
    echo "   🎤 音频模式: PulseAudio虚拟化"
    echo "   🔄 虚拟音频源: $VIRTUAL_SOURCE"
    echo "   ✅ 与推流音频无冲突"
else
    echo "   🎤 音频模式: 默认设备"
    echo "   ⚠️  可能与推流音频冲突"
    echo "   💡 建议运行 smart-stream-deploy.sh 配置虚拟音频源"
fi
echo ""
echo "🚀 WebRTC服务将在系统启动时自动运行"

# 配置WebRTC日志轮转
echo "🧹 配置WebRTC日志轮转（保留7天）..."
sudo tee /etc/logrotate.d/webrtc-logs > /dev/null << EOF
/home/<USER>/srs/logs/webrtc.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 orangepi orangepi
    copytruncate
}
EOF

echo "✅ WebRTC日志轮转配置完成"
echo ""
echo "📋 管理命令:"
echo "   启动服务: $SCRIPT_FILE"
echo "   查看进程: ps aux | grep chromium"
echo "   停止服务: pkill chromium"
echo "   查看日志: tail -f /home/<USER>/srs/logs/webrtc.log"
echo "   清理日志: > /home/<USER>/srs/logs/webrtc.log"
echo "   强制轮转: sudo logrotate -f /etc/logrotate.d/webrtc-logs"
echo ""
echo "🔗 访问地址:"
echo "   WebRTC页面: $SUCCESS_PAGE_BASE?roomId=$ROOM_ID&autoJoin=true"