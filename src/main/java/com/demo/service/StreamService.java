package com.demo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;
import java.util.HashSet;

import static com.demo.controller.SrsCallbackController.SRS_MAP;

@Slf4j
@Service
public class StreamService {

    // 静态配置：永久允许推流的流名称列表
    private static final Set<String> PERMANENT_ALLOWED_STREAMS = new HashSet<String>() {{
//        add("c0742bfc6c031921680120"); // 您的流
//        add("c0742bfc6c031921680130");
        add("c0742bfc6ffd1921680120");
        add("c0742bfc6ffd1921680130");

        // 可以在这里添加更多需要永久推流的流名称
    }};

    // 观看者计数：<streamName, viewerCount>
    private final ConcurrentHashMap<String, Integer> viewerCounts = new ConcurrentHashMap<>();

    // 推流权限状态：<streamName, isAllowed>
    private final ConcurrentHashMap<String, Boolean> publishPermissions = new ConcurrentHashMap<>();
    
    /**
     * 有观看者开始观看
     * @param streamName 流名称
     * @return 当前观看者数量
     */
    public synchronized int addViewer(String streamName) {
        int currentCount = viewerCounts.getOrDefault(streamName, 0);
        currentCount++;
        viewerCounts.put(streamName, currentCount);
        
        // 如果是第一个观看者，允许推流
        if (currentCount == 1) {
            publishPermissions.put(streamName, true);
            log.info("流 {} 首次有观看者，允许推流", SRS_MAP.get(streamName));
        }
        
        log.info("流 {} 增加观看者，当前观看者数: {}", SRS_MAP.get(streamName), currentCount);
        return currentCount;
    }
    
    /**
     * 观看者停止观看
     * @param streamName 流名称
     * @return 当前观看者数量
     */
    public synchronized int removeViewer(String streamName) {
        int currentCount = viewerCounts.getOrDefault(streamName, 0);
        if (currentCount > 0) {
            currentCount--;
            viewerCounts.put(streamName, currentCount);
        }
        
        // 如果没有观看者了，检查是否在永久允许列表中
        if (currentCount == 0) {
            // 如果不在永久允许列表中，才取消推流权限
            if (!PERMANENT_ALLOWED_STREAMS.contains(streamName)) {
                publishPermissions.put(streamName, false);
                log.info("流 {} 无观看者，取消推流权限", SRS_MAP.get(streamName));
            } else {
                log.info("流 {} 无观看者，但在永久推流列表中，保持推流权限", SRS_MAP.get(streamName));
            }
        }
        
        log.info("流 {} 减少观看者，当前观看者数: {}", SRS_MAP.get(streamName), currentCount);
        return currentCount;
    }
    
    /**
     * 检查是否允许推流
     * @param streamName 流名称
     * @return 是否允许推流
     */
    public boolean isPublishAllowed(String streamName) {
        // 优先检查静态永久权限列表，然后检查动态权限
        return PERMANENT_ALLOWED_STREAMS.contains(streamName) ||
               publishPermissions.getOrDefault(streamName, false);
    }
    
    /**
     * 获取当前观看者数量
     * @param streamName 流名称
     * @return 观看者数量
     */
    public int getViewerCount(String streamName) {
        return viewerCounts.getOrDefault(streamName, 0);
    }
    
    /**
     * 手动设置推流权限（用于特殊情况）
     * @param streamName 流名称
     * @param allowed 是否允许
     */
    public void setPublishPermission(String streamName, boolean allowed) {
        publishPermissions.put(streamName, allowed);
        log.info("手动设置流 {} 推流权限: {}", SRS_MAP.get(streamName), allowed);
    }
    
    /**
     * 推流结束时清理状态
     * @param streamName 流名称
     */
    public synchronized void onPublishStop(String streamName) {
        // 如果不在永久允许列表中，才清理权限状态
        if (!PERMANENT_ALLOWED_STREAMS.contains(streamName)) {
            publishPermissions.remove(streamName);
            log.info("流 {} 推流结束，清理推流权限", SRS_MAP.get(streamName));
        } else {
            log.info("流 {} 推流结束，但在永久推流列表中，保留推流权限", SRS_MAP.get(streamName));
        }

        // 观看者计数总是清理
        viewerCounts.remove(streamName);
        log.info("流 {} 推流结束，清理观看者计数", SRS_MAP.get(streamName));
    }
    


    /**
     * 检查流是否在永久允许列表中
     * @param streamName 流名称
     * @return 是否永久允许
     */
    public boolean isPermanentAllowed(String streamName) {
        return PERMANENT_ALLOWED_STREAMS.contains(streamName);
    }

    /**
     * 获取所有流的状态信息（用于监控）
     * @return 流状态映射
     */
    public ConcurrentHashMap<String, Object> getAllStreamStatus() {
        ConcurrentHashMap<String, Object> status = new ConcurrentHashMap<>();

        // 添加所有有权限的流（包括永久和临时）
        Set<String> allStreams = new HashSet<>(publishPermissions.keySet());
        allStreams.addAll(PERMANENT_ALLOWED_STREAMS);

        for (String streamName : allStreams) {
            ConcurrentHashMap<String, Object> streamInfo = new ConcurrentHashMap<>();
            streamInfo.put("viewerCount", getViewerCount(streamName));
            streamInfo.put("publishAllowed", isPublishAllowed(streamName));
            streamInfo.put("permanentAllowed", isPermanentAllowed(streamName));
            status.put(streamName, streamInfo);
        }

        return status;
    }
    

}
